'use server';

import { WorkflowCreateRequest, WorkflowResponse, WorkflowUpdateRequest } from '@/types/workflow.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getWorkflows() {
  return await HttpService.get<ApiResponseCollection<WorkflowResponse>>('/workflow');
}

export async function getWorkflowById(id: number) {
  return await HttpService.get<ApiResponseResource<WorkflowResponse>>(`/workflow/${id}`);
}

export async function createWorkflow(payload: WorkflowCreateRequest) {
  return await HttpService.post<ApiResponseResource<WorkflowResponse>, WorkflowCreateRequest>(
    '/workflow',
    payload,
  );
}

export async function updateWorkflow(id: number, payload: WorkflowUpdateRequest) {
  return await HttpService.put<ApiResponseResource<WorkflowResponse>, WorkflowUpdateRequest>(
    `/workflow/${id}`,
    payload,
  );
}

export async function deleteWorkflow(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/workflow/${id}`);
}
