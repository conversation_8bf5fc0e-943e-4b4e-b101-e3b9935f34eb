import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import CreateWorkflowForm from '@/components/forms/workflow/CreateWorkflowForm';
import { getDepartments } from '@/app/services/department.service';
import { getUsers } from '@/app/services/user.service';
import { getUsergroups } from '@/app/services/usergroup.service';

const WorkFlowCreatePage = async () => {
  const [departments, users, usergroups] = await Promise.all([
    getDepartments(),
    getUsers(),
    getUsergroups(),
  ]);

  return (
    <>
      <HeaderBreadcrumb
        content="Yeni İş Akışı Ekle"
        links={[{ title: 'İş Akışları', link: ROUTES.WORKFLOW.INDEX }, { title: 'İş Akışı Ekle' }]}
      />
      <KBCard className="m-4 p-4">
        <CreateWorkflowForm
          departments={departments.status ? departments.data.items : []}
          users={users.status ? users.data.items : []}
          usergroups={usergroups.status ? usergroups.data.items : []}
        />
      </KBCard>
    </>
  );
};

export default WorkFlowCreatePage;
