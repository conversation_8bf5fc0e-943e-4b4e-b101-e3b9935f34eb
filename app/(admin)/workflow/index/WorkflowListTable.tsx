'use client';

import { useEffect, useState } from 'react';
import { Button, Popconfirm, Space, Switch, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { deleteWorkflow, getWorkflows } from '@/app/services/workflow.service';
import { WorkflowResponse } from '@/types/workflow.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';

const WorkflowListTable = () => {
  const [workflows, setWorkflows] = useState<WorkflowResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    fetchWorkflows();
  }, []);

  const fetchWorkflows = async () => {
    setLoading(true);
    const response = await getWorkflows();
    if (response.status) {
      setWorkflows(response.data.items);
    }
    setLoading(false);
  };

  const handleDelete = async (id: number) => {
    const res = await deleteWorkflow(id);
    if (res.status) {
      toast.success('İş akışı başarıyla silindi');
      fetchWorkflows();
    } else {
      toast.error(res.message);
    }
  };

  const columns: ColumnsType<WorkflowResponse> = [
    {
      title: 'İş Akışı Adı',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => <span className="font-medium">{text}</span>,
    },
    {
      title: 'Açıklama',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => text || '-',
    },
    {
      title: 'Adım Sayısı',
      key: 'steps_count',
      render: (_, record) => (
        <Tag color="blue">{record.steps?.length || 0} Adım</Tag>
      ),
    },
    {
      title: 'Durum',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? 'Aktif' : 'Pasif'}
        </Tag>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => router.push(ROUTES.WORKFLOW.UPDATE(record.id))}
          >
            Düzenle
          </Button>
          <Popconfirm
            title="İş akışını sil"
            description="Bu iş akışını silmek istediğinizden emin misiniz?"
            onConfirm={() => handleDelete(record.id)}
            okText="Evet"
            cancelText="Hayır"
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <KBCard>
      <Table
        columns={columns}
        dataSource={workflows}
        loading={loading}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} / ${total} kayıt`,
        }}
      />
    </KBCard>
  );
};

export default WorkflowListTable;
