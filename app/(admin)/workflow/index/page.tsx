import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import WorkflowListTable from '@/app/(admin)/workflow/index/WorkflowListTable';

const WorkFlowIndexPage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.WORKFLOW.CREATE}>
            <Button type="primary">İş Akışı Ekle</Button>
          </Link>
        }
        links={[{ title: 'İş Akışları' }]}
      />
      <div className="m-4">
        <WorkflowListTable />
      </div>
    </>
  );
};

export default WorkFlowIndexPage;
