'use client';

import { useState } from 'react';
import { Button, Form, Input, Switch, Space, Card, Modal } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { WorkflowCreateRequest, WorkflowStepCreateRequest } from '@/types/workflow.types';
import { DepartmentResponse } from '@/types/department.types';
import { UserResponse } from '@/types/user.types';
import { UsergroupResponse } from '@/types/usergroup.types';
import { createWorkflow } from '@/app/services/workflow.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import WorkflowStepModal from './WorkflowStepModal';

interface CreateWorkflowFormProps {
  departments: DepartmentResponse[];
  users: UserResponse[];
  usergroups: UsergroupResponse[];
}

const CreateWorkflowForm = ({ departments, users, usergroups }: CreateWorkflowFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);
  const [steps, setSteps] = useState<WorkflowStepCreateRequest[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingStep, setEditingStep] = useState<WorkflowStepCreateRequest | null>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const onFinish = async (values: { name: string; description: string; is_active: boolean }) => {
    const payload: WorkflowCreateRequest = {
      ...values,
      steps: steps.map((step, index) => ({
        ...step,
        step_order: index + 1,
      })),
    };

    const res = await createWorkflow(payload);
    formHandle(res, () => {
      router.push(ROUTES.WORKFLOW.INDEX);
    });
  };

  const handleAddStep = () => {
    setEditingStep(null);
    setEditingIndex(null);
    setIsModalOpen(true);
  };

  const handleEditStep = (step: WorkflowStepCreateRequest, index: number) => {
    setEditingStep(step);
    setEditingIndex(index);
    setIsModalOpen(true);
  };

  const handleDeleteStep = (index: number) => {
    const newSteps = steps.filter((_, i) => i !== index);
    setSteps(newSteps);
  };

  const handleStepSave = (step: WorkflowStepCreateRequest) => {
    if (editingIndex !== null) {
      const newSteps = [...steps];
      newSteps[editingIndex] = step;
      setSteps(newSteps);
    } else {
      setSteps([...steps, step]);
    }
    setIsModalOpen(false);
    setEditingStep(null);
    setEditingIndex(null);
  };

  const getStepTypeLabel = (type: string) => {
    switch (type) {
      case 'simple':
        return 'Basit Durum';
      case 'conditional':
        return 'Koşullu Durum';
      case 'conditional_approver':
        return 'Koşullu Onaylayan';
      default:
        return type;
    }
  };

  const getApproverLabel = (step: WorkflowStepCreateRequest) => {
    const approver = 
      step.approver_type === 'user' 
        ? users.find(u => u.id === step.approver_id)?.first_name + ' ' + users.find(u => u.id === step.approver_id)?.last_name
        : step.approver_type === 'department'
        ? departments.find(d => d.id === step.approver_id)?.name
        : usergroups.find(ug => ug.id === step.approver_id)?.name;
    
    return approver || 'Bilinmeyen';
  };

  return (
    <>
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Form.Item
          label="İş Akışı Adı"
          name="name"
          rules={[{ required: true, message: 'İş akışı adı zorunludur.' }]}
        >
          <Input placeholder="İş akışı adını giriniz" />
        </Form.Item>

        <Form.Item
          label="Açıklama"
          name="description"
        >
          <Input.TextArea placeholder="İş akışı açıklamasını giriniz" rows={3} />
        </Form.Item>

        <Form.Item
          label="Aktif"
          name="is_active"
          valuePropName="checked"
          initialValue={true}
        >
          <Switch />
        </Form.Item>

        <div className="mb-4">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-medium">İş Akışı Adımları</h3>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddStep}>
              Adım Ekle
            </Button>
          </div>

          <div className="space-y-3">
            {steps.map((step, index) => (
              <Card key={index} size="small" className="border-l-4 border-l-blue-500">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium">
                        Adım {index + 1}
                      </span>
                      <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm">
                        {getStepTypeLabel(step.step_type)}
                      </span>
                    </div>
                    <h4 className="font-medium mb-1">{step.name}</h4>
                    <p className="text-sm text-gray-600">
                      Onaylayan: {getApproverLabel(step)} ({step.approver_type})
                    </p>
                    {step.condition_field && (
                      <p className="text-sm text-gray-500 mt-1">
                        Koşul: {step.condition_field} {step.condition_operator} {step.condition_value}
                      </p>
                    )}
                  </div>
                  <Space>
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEditStep(step, index)}
                    />
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => handleDeleteStep(index)}
                    />
                  </Space>
                </div>
              </Card>
            ))}
            {steps.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                Henüz adım eklenmemiş. Yukarıdaki "Adım Ekle" butonunu kullanarak başlayın.
              </div>
            )}
          </div>
        </div>

        <Form.Item>
          <Button type="primary" htmlType="submit" className="mt-4">
            İş Akışı Oluştur
          </Button>
        </Form.Item>
      </Form>

      <WorkflowStepModal
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        onSave={handleStepSave}
        departments={departments}
        users={users}
        usergroups={usergroups}
        editingStep={editingStep}
      />
    </>
  );
};

export default CreateWorkflowForm;
