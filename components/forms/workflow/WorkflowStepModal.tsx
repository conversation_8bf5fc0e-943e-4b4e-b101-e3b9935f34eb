'use client';

import { useEffect, useState } from 'react';
import { Modal, Form, Input, Select, InputNumber, Button, Space } from 'antd';
import { WorkflowStepCreateRequest } from '@/types/workflow.types';
import { DepartmentResponse } from '@/types/department.types';
import { UserResponse } from '@/types/user.types';
import { UsergroupResponse } from '@/types/usergroup.types';

interface WorkflowStepModalProps {
  open: boolean;
  onCancel: () => void;
  onSave: (step: WorkflowStepCreateRequest) => void;
  departments: DepartmentResponse[];
  users: UserResponse[];
  usergroups: UsergroupResponse[];
  editingStep?: WorkflowStepCreateRequest | null;
}

const WorkflowStepModal = ({
  open,
  onCancel,
  onSave,
  departments,
  users,
  usergroups,
  editingStep,
}: WorkflowStepModalProps) => {
  const [form] = Form.useForm();
  const [stepType, setStepType] = useState<string>('simple');
  const [approverType, setApproverType] = useState<string>('user');
  const [showStepTypeModal, setShowStepTypeModal] = useState(true);

  useEffect(() => {
    if (open) {
      if (editingStep) {
        form.setFieldsValue(editingStep);
        setStepType(editingStep.step_type);
        setApproverType(editingStep.approver_type);
        setShowStepTypeModal(false);
      } else {
        form.resetFields();
        setStepType('simple');
        setApproverType('user');
        setShowStepTypeModal(true);
      }
    }
  }, [open, editingStep, form]);

  const handleStepTypeSelect = (type: string) => {
    setStepType(type);
    setShowStepTypeModal(false);
    form.setFieldValue('step_type', type);
  };

  const handleSave = () => {
    form.validateFields().then((values) => {
      const step: WorkflowStepCreateRequest = {
        ...values,
        step_type: stepType,
        step_order: 0, // Will be set by parent component
      };
      onSave(step);
      form.resetFields();
    });
  };

  const handleCancel = () => {
    form.resetFields();
    setShowStepTypeModal(true);
    onCancel();
  };

  const getApproverOptions = () => {
    switch (approverType) {
      case 'user':
        return users.map(user => ({
          label: `${user.first_name} ${user.last_name}`,
          value: user.id,
        }));
      case 'department':
        return departments.map(dept => ({
          label: dept.name,
          value: dept.id,
        }));
      case 'role':
        return usergroups.map(group => ({
          label: group.name,
          value: group.id,
        }));
      default:
        return [];
    }
  };

  // İlk modal: Bileşen türü seçimi (resimlerinizdeki gibi)
  if (showStepTypeModal && !editingStep) {
    return (
      <Modal
        title="Yeni Bileşen Ekle"
        open={open}
        onCancel={handleCancel}
        footer={null}
        width={400}
      >
        <div className="text-center mb-4">
          <p className="text-gray-600">Bileşen Türü Seçiniz</p>
        </div>
        <Space direction="vertical" className="w-full" size="middle">
          <Button
            block
            size="large"
            onClick={() => handleStepTypeSelect('conditional')}
            className="h-12"
          >
            Koşullu Durum Ekle
          </Button>
          <Button
            block
            size="large"
            onClick={() => handleStepTypeSelect('conditional_approver')}
            className="h-12"
          >
            Koşullu Onaylayan Ekle
          </Button>
          <Button
            block
            size="large"
            onClick={() => handleStepTypeSelect('simple')}
            className="h-12"
          >
            Basit Durum Ekle
          </Button>
        </Space>
      </Modal>
    );
  }

  // İkinci modal: Adım detayları (resimlerinizdeki form)
  return (
    <Modal
      title={editingStep ? "Adım Güncelle" : "Koşul Ekle"}
      open={open}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          İptal
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          {editingStep ? "GÜNCELLE" : "OLUŞTUR"}
        </Button>,
      ]}
      width={600}
    >
      <div className="mb-4">
        <p className="text-gray-600">İş Akışı Tanımı</p>
      </div>

      <Form form={form} layout="vertical">
        <Form.Item
          label="Adım Adı"
          name="name"
          rules={[{ required: true, message: 'Adım adı zorunludur.' }]}
        >
          <Input placeholder="Adım adını giriniz" />
        </Form.Item>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="mb-2 font-medium">Onaylayan</p>
            <Form.Item
              label="Onay Grubu"
              name="approver_type"
              rules={[{ required: true, message: 'Onay grubu seçimi zorunludur.' }]}
            >
              <Select
                placeholder="Seçiniz"
                onChange={setApproverType}
                options={[
                  { label: 'Kullanıcı', value: 'user' },
                  { label: 'Departman', value: 'department' },
                  { label: 'Rol', value: 'role' },
                ]}
              />
            </Form.Item>
          </div>

          <div>
            <Form.Item
              label="Onaylayan"
              name="approver_id"
              rules={[{ required: true, message: 'Onaylayan seçimi zorunludur.' }]}
            >
              <Select
                placeholder="Seçiniz"
                options={getApproverOptions()}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </div>
        </div>

        {(stepType === 'conditional' || stepType === 'conditional_approver') && (
          <>
            <div className="border-t pt-4 mt-4">
              <p className="mb-3 font-medium">Koşul Ayarları</p>
              
              <div className="grid grid-cols-3 gap-4">
                <Form.Item
                  label="Alan"
                  name="condition_field"
                  rules={[{ required: true, message: 'Koşul alanı zorunludur.' }]}
                >
                  <Select
                    placeholder="Seçiniz"
                    options={[
                      { label: 'Tutar', value: 'amount' },
                      { label: 'Departman', value: 'department_id' },
                      { label: 'Kullanıcı', value: 'user_id' },
                    ]}
                  />
                </Form.Item>

                <Form.Item
                  label="Operatör"
                  name="condition_operator"
                  rules={[{ required: true, message: 'Operatör seçimi zorunludur.' }]}
                >
                  <Select
                    placeholder="Seçiniz"
                    options={[
                      { label: 'Eşittir', value: 'equals' },
                      { label: 'Büyüktür', value: 'greater_than' },
                      { label: 'Küçüktür', value: 'less_than' },
                    ]}
                  />
                </Select>
                </Form.Item>

                <Form.Item
                  label="Değer"
                  name="condition_value"
                  rules={[{ required: true, message: 'Koşul değeri zorunludur.' }]}
                >
                  <Input placeholder="Değer giriniz" />
                </Form.Item>
              </div>
            </div>
          </>
        )}
      </Form>
    </Modal>
  );
};

export default WorkflowStepModal;
