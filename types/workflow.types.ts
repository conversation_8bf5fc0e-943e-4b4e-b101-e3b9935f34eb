export type WorkflowResponse = {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  steps: WorkflowStepResponse[];
  organization_id: number;
  created_at: string;
  updated_at: string;
};

export type WorkflowStepResponse = {
  id: number;
  workflow_id: number;
  name: string;
  step_order: number;
  step_type: 'simple' | 'conditional' | 'conditional_approver';
  approver_type: 'user' | 'department' | 'role';
  approver_id: number;
  condition_field?: 'amount' | 'department_id' | 'user_id';
  condition_operator?: 'equals' | 'greater_than' | 'less_than';
  condition_value?: string;
  approver?: {
    id: number;
    name: string;
    type: string;
  };
};

export type WorkflowCreateRequest = {
  name: string;
  description: string;
  is_active: boolean;
  steps: WorkflowStepCreateRequest[];
};

export type WorkflowStepCreateRequest = {
  name: string;
  step_order: number;
  step_type: 'simple' | 'conditional' | 'conditional_approver';
  approver_type: 'user' | 'department' | 'role';
  approver_id: number;
  condition_field?: 'amount' | 'department_id' | 'user_id';
  condition_operator?: 'equals' | 'greater_than' | 'less_than';
  condition_value?: string;
};

export type WorkflowUpdateRequest = WorkflowCreateRequest;
